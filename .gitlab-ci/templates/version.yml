# Version extraction job templates

# Base version extraction template
.extract-version:
  tags:
    - ${RUNNER_TAG_EXTRA_SMALL}
  image: $RUNNER_IMAGE_ALPINE
  stage: build
  script:
    - |
      # Install necessary tools
      apk add --no-cache jq grep sed

      # Create build.env file to store versions
      touch build.env

      # Check if VERSION_FILE is provided
      if [ -z "$VERSION_FILE" ]; then
        echo "ERROR: VERSION_FILE variable is not set"
        exit 1
      fi

      # Check if SERVICE_NAME is provided
      if [ -z "$SERVICE_NAME" ]; then
        echo "ERROR: SERVICE_NAME variable is not set"
        exit 1
      fi

      # Full path to the version file
      FULL_PATH="${CI_PROJECT_DIR}/${VERSION_FILE}"

      # Check if file exists
      if [ ! -f "$FULL_PATH" ]; then
        echo "ERROR: Version file not found at $FULL_PATH"
        exit 1
      fi

      # Determine file type and extract version accordingly
      if [[ "$VERSION_FILE" == *.json ]]; then
        # Extract version from JSON file (package.json)
        VERSION=$(jq -r '.version' "$FULL_PATH")
        echo "Extracted version from JSON file: $VERSION"
      elif [[ "$VERSION_FILE" == *.gradle ]]; then
        # Extract version from Gradle file (build.gradle)
        # Updated regex to handle version suffixes like -SNAPSHOT, -ALPHA, -BETA, etc.
        VERSION=$(grep -E "^version = '[0-9]+(\.[0-9]+)*(-[A-Za-z0-9]+)*'" "$FULL_PATH" | sed -E "s/version = '([0-9]+(\.[0-9]+)*(-[A-Za-z0-9]+)*)'.*$/\1/")
        # If not found, try with double quotes
        if [ -z "$VERSION" ]; then
          VERSION=$(grep -E "^version = \"[0-9]+(\.[0-9]+)*(-[A-Za-z0-9]+)*\"" "$FULL_PATH" | sed -E "s/version = \"([0-9]+(\.[0-9]+)*(-[A-Za-z0-9]+)*)\".*$/\1/")
        fi
        echo "Extracted version from Gradle file: $VERSION"
      else
        echo "ERROR: Unsupported file type for version extraction: $VERSION_FILE"
        echo "Supported file types: *.json, *.gradle"
        exit 1
      fi

      # Check if version was successfully extracted
      if [ -z "$VERSION" ]; then
        echo "ERROR: Failed to extract version from $FULL_PATH"
        exit 1
      fi

      # Set version tag based on branch
      if [ "$CI_COMMIT_BRANCH" = "main" ]; then
        # For main branch, use the version as is
        VERSION_TAG="$VERSION"
      else
        # For other branches, append the short commit hash
        VERSION_TAG="$VERSION-${CI_COMMIT_SHORT_SHA}"
      fi

      echo "Using version tag for $SERVICE_NAME: $VERSION_TAG"

      # Convert SERVICE_NAME to uppercase for environment variable
      SERVICE_VAR=$(echo "$SERVICE_NAME" | tr '-' '_' | tr '[:lower:]' '[:upper:]')

      # Export version for other jobs to use
      echo "${SERVICE_VAR}_VERSION=$VERSION" >> build.env
      echo "${SERVICE_VAR}_VERSION_TAG=$VERSION_TAG" >> build.env
  artifacts:
    reports:
      dotenv: build.env
