# Build job templates

# Base build job template
.build-base:
  tags:
    - ${RUNNER_TAG_MEDIUM}
  artifacts:
    expire_in: 4 weeks

# Java/Spring build template
.build-java:
  extends: .build-base
  image: $RUNNER_IMAGE_GRADLE
  script:
    - cd ${PROJECT_PATH}
    - gradle build -x test
  artifacts:
    paths:
      - ${PROJECT_PATH}/build/libs/*.jar

# Angular build template
.build-angular:
  extends: .build-base
  image: $RUNNER_IMAGE_NODE
  before_script:
    - |
      echo "Preparing web-app build..."
      cd "${CI_PROJECT_DIR}/${PROJECT_PATH}"
      if [ "$CI_SERVER_HOST" = "$ADESSO_GITLAB_HOST" ]; then
        # For adesso, only append token to npm config
        echo "//${NPM_SDK_REGISTRY_URL}/:_authToken=${NPM_SDK_REGISTRY_TOKEN}" >> .npmrc
      else 
        # For Dataport, we need to modify the whole NPM configuration and recreate the package-lock.json
        rm -f package-lock.json
        rm -f .npmrc
        echo "@digifischdok:registry=https://${NPM_SDK_REGISTRY_URL}" > .npmrc
        echo "//${NPM_SDK_REGISTRY_URL}/:_authToken=${NPM_SDK_REGISTRY_TOKEN}" >> .npmrc
        echo "legacy-peer-deps=true" >> .npmrc
        npm i --package-lock-only --production
      fi
      cd "${CI_PROJECT_DIR}"
  script:
    - cd ${PROJECT_PATH}
    - npm ci
    - npm run build
  artifacts:
    paths:
      - ${PROJECT_PATH}/dist/
