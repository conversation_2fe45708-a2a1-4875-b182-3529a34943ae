import { cva } from 'class-variance-authority';

export const cardHeaderStyles = cva(['h-24', 'pl-6', 'pr-8', 'rounded-t-lg', 'flex', 'flex-row', 'items-center', 'backdrop-blur-xl', 'gap-4'], {
  variants: {
    variant: {
      primary: ['shadow-glass-darkened', 'bg-tint-white', 'border-border-outline-glass', 'border', 'font-extra-bold'],
      secondary: [],
      error: ['text-feedback-text-warning'],
    },
  },
  defaultVariants: {
    variant: 'primary',
  },
});
