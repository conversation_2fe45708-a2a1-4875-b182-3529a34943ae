package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CreateVacationLicenseCommandValidatorTest {

    private CreateVacationLicenseCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new CreateVacationLicenseCommandValidator(countryService, tenantRulesValidationPort);

    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        RegisterEntry registerEntry = new RegisterEntry();

        final CreateVacationLicenseCommand command = new CreateVacationLicenseCommand(
                UUID.randomUUID(),
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createValidityPeriod(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(command, registerEntry));
    }


    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        RegisterEntry registerEntry = new RegisterEntry();

        final CreateVacationLicenseCommand invalidCommand = new CreateVacationLicenseCommand(
                UUID.randomUUID(),
                "salt",
                null,
                List.of(),
                List.of(),
                null,
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class,
                () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        ValidationResult result = exception.getValidationResult();

        // THEN
        assertTrue(result.hasErrors());
        assertEquals(4, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("Fees are required"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
        assertTrue(result.getErrorNotes().contains("Validity Period is required"));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid or salt is missing")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        RegisterEntry registerEntry = new RegisterEntry();

        final CreateVacationLicenseCommand invalidCommand = new CreateVacationLicenseCommand(
                UUID.randomUUID(),
                "",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createValidityPeriod(),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class,
                () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException  when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        RegisterEntry registerEntry = new RegisterEntry();

        // Create user details with null office
       UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        final CreateVacationLicenseCommand invalidCommand = new CreateVacationLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createValidityPeriod(),
                userDetailsWithoutOffice
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class,
                () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should throw a ClientInputValidationException when a vacation license already exists for the same year and federal state")
    public void testValidateOrThrow_licenseAlreadyExistsForYearAndState() {
        // GIVEN
        UUID registerId = UUID.randomUUID();
        FederalState federalState = FederalState.SH;
        int currentYear = LocalDate.now().getYear();

        // Create a register entry with an existing vacation license for the current year
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerId);

        // Create a vacation license with validity period in the current year
        FishingLicense existingLicense = new FishingLicense();
        existingLicense.setType(LicenseType.VACATION);
        existingLicense.setNumber("SH12345678901234");
        existingLicense.setIssuingFederalState(federalState);

        // Create and add a validity period for the current year
        ValidityPeriod existingValidityPeriod = new ValidityPeriod();
        existingValidityPeriod.setValidFrom(LocalDate.of(currentYear, 1, 1));
        existingValidityPeriod.setValidTo(LocalDate.of(currentYear, 12, 31));

        List<ValidityPeriod> validityPeriods = new ArrayList<>();
        validityPeriods.add(existingValidityPeriod);
        existingLicense.setValidityPeriods(validityPeriods);

        // Add the license to the register entry
        registerEntry.getFishingLicenses().add(existingLicense);

        // Create a command to create another vacation license for the same year and federal state
        ValidityPeriod newValidityPeriod = new ValidityPeriod();
        newValidityPeriod.setValidFrom(LocalDate.of(currentYear, 6, 1)); // Different month but same year
        newValidityPeriod.setValidTo(LocalDate.of(currentYear, 6, 30));

        final CreateVacationLicenseCommand command = new CreateVacationLicenseCommand(
                registerId,
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                newValidityPeriod,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class,
                () -> commandValidator.validateOrThrow(command, registerEntry));
        ValidationResult result = exception.getValidationResult();

        // THEN
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("A Vacation License for the same year and federal state already exists"));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should not throw an error when a vacation license exists for a different year")
    public void testValidateOrThrow_licenseExistsForDifferentYear() {
        // GIVEN
        UUID registerId = UUID.randomUUID();
        FederalState federalState = FederalState.SH;
        int currentYear = LocalDate.now().getYear();

        // Create a register entry with an existing vacation license for the previous year
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerId);

        // Create a vacation license with validity period in the previous year
        FishingLicense existingLicense = new FishingLicense();
        existingLicense.setType(LicenseType.VACATION);
        existingLicense.setNumber("SH12345678901234");
        existingLicense.setIssuingFederalState(federalState);

        // Create and add a validity period for the previous year
        ValidityPeriod existingValidityPeriod = new ValidityPeriod();
        existingValidityPeriod.setValidFrom(LocalDate.of(currentYear - 1, 1, 1));
        existingValidityPeriod.setValidTo(LocalDate.of(currentYear - 1, 12, 31));

        List<ValidityPeriod> validityPeriods = new ArrayList<>();
        validityPeriods.add(existingValidityPeriod);
        existingLicense.setValidityPeriods(validityPeriods);

        // Add the license to the register entry
        registerEntry.getFishingLicenses().add(existingLicense);

        // Create a command to create another vacation license for the current year
        ValidityPeriod newValidityPeriod = new ValidityPeriod();
        newValidityPeriod.setValidFrom(LocalDate.of(currentYear, 1, 1));
        newValidityPeriod.setValidTo(LocalDate.of(currentYear, 12, 31));

        final CreateVacationLicenseCommand command = new CreateVacationLicenseCommand(
                registerId,
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                newValidityPeriod,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN/THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(command, registerEntry));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should not throw an error when a vacation license exists for a different federal state")
    public void testValidateOrThrow_licenseExistsForDifferentFederalState() {
        // GIVEN
        UUID registerId = UUID.randomUUID();
        int currentYear = LocalDate.now().getYear();

        // Create a register entry with an existing vacation license for a different federal state
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerId);

        // Create a vacation license with validity period in the current year but different federal state
        FishingLicense existingLicense = new FishingLicense();
        existingLicense.setType(LicenseType.VACATION);
        existingLicense.setNumber("HH12345678901234");
        existingLicense.setIssuingFederalState(FederalState.HH); // Different federal state

        // Create and add a validity period for the current year
        ValidityPeriod existingValidityPeriod = new ValidityPeriod();
        existingValidityPeriod.setValidFrom(LocalDate.of(currentYear, 1, 1));
        existingValidityPeriod.setValidTo(LocalDate.of(currentYear, 12, 31));

        List<ValidityPeriod> validityPeriods = new ArrayList<>();
        validityPeriods.add(existingValidityPeriod);
        existingLicense.setValidityPeriods(validityPeriods);

        // Add the license to the register entry
        registerEntry.getFishingLicenses().add(existingLicense);

        // Create a command to create another vacation license for the same year but different federal state
        ValidityPeriod newValidityPeriod = new ValidityPeriod();
        newValidityPeriod.setValidFrom(LocalDate.of(currentYear, 1, 1));
        newValidityPeriod.setValidTo(LocalDate.of(currentYear, 12, 31));

        final CreateVacationLicenseCommand command = new CreateVacationLicenseCommand(
                registerId,
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                newValidityPeriod,
                DomainTestData.createUserDetails(UserRole.OFFICIAL) // This will use SH as federal state
        );

        // WHEN/THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(command, registerEntry));
    }

    @Test
    @DisplayName("CreateVacationLicenseCommandValidator.validateOrThrow should throw UnsupportedOperationException when called without RegisterEntry")
    public void testValidateOrThrow_withoutRegisterEntry() {
        // GIVEN
        final CreateVacationLicenseCommand command = new CreateVacationLicenseCommand(
                UUID.randomUUID(),
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createFee()),
                List.of(DomainTestData.createTax()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createValidityPeriod(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN/THEN
        UnsupportedOperationException exception = assertThrows(UnsupportedOperationException.class,
                () -> commandValidator.validateOrThrow(command));

        assertEquals("Validation without RegisterEntry for the CreateVacationLicenseCommandValidator is not supported.",
                exception.getMessage());
    }
}