package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.ChangePersonalDataCommand;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class ChangePersonalDataTest {

    @Test
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();
        final List<Tax> givenTaxes = DomainTestData.createTaxesWithOneTax();
        final String givenOriginalSalt = "12345";
        final QualificationsProof givenQualificationsProof = new QualificationsProof();
        givenQualificationsProof.setFishingCertificateId("4711");
        givenQualificationsProof.setIssuedBy("Fischfreunde Övelgönne e.V.");
        givenQualificationsProof.setPassedOn(LocalDate.now().minusYears(1));
        givenQualificationsProof.setFederalState("SH");
        givenQualificationsProof.setType(QualificationsProofType.CERTIFICATE);

        ConsentInfo givenConsentInfo = DomainTestData.createConsentInfo();


        Person changedPerson = DomainTestData.createPerson();
        changedPerson.setFirstname("new firstname");

        fixture.givenCommands(
                        new DigitizeRegularLicenseCommand(
                                givenId,
                                givenOriginalSalt,
                                givenPerson,
                                List.of(DomainTestData.createFee()),
                                List.of(),
                                List.of(),
                                List.of(givenQualificationsProof),
                                givenConsentInfo,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .when(
                        new ChangePersonalDataCommand(
                                givenId,
                                changedPerson,
                                givenTaxes,
                                givenConsentInfo,
                                "salt",
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(PersonalDataChangedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert register ID
                    assertEquals(givenId, registerEntry.getRegisterId());

                    // Assert person is created
                    assertNotNull(registerEntry.getPerson());

                    // Assert person is changed
                    assertNotNull(registerEntry.getPerson().getFirstname().equalsIgnoreCase(givenPerson.getFirstname()));

                    assertEquals(3, registerEntry.getIdentificationDocuments().size());

                });

    }
}
