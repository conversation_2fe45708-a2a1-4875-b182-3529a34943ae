package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.commands.OrderReplacementCardCommand;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;
import java.util.UUID;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

public class OrderReplacementCardTest {

    @Test
    @DisplayName("Test whether aggregate handlers OrderReplacementCardCommand of are successful.")
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();
        final UserDetails givenUserDetails = DomainTestData.createUserDetails(UserRole.OFFICIAL);

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);
        final FishingLicense givenFishingLicense = new FishingLicense();
        givenFishingLicense.setNumber("LN12345");


        fixture
                .givenCommands(
                        new CreateFishingCertificateCommand(givenId,
                                DomainTestData.jan_2024,
                                givenPerson,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        ),
                        new MoveJurisdictionCommand(givenId, DomainTestData.createJurisdictionConsentInfo(), givenOriginalSalt, emptyList(), givenUserDetails),
                        new CreateRegularLicenseCommand(givenId, givenOriginalSalt, DomainTestData.createConsentInfo(), givenPerson, List.of(DomainTestData.createFee()), emptyList(), givenUserDetails))
                .when(new OrderReplacementCardCommand(givenId, givenFishingLicense.getNumber(), givenPerson, givenOriginalSalt, List.of(DomainTestData.createFee()), List.of(), DomainTestData.createConsentInfo(), givenUserDetails))
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload -> payload.getClass().isAssignableFrom(ReplacementCardOrderedEvent.class)))))
                .expectState(registerEntry -> {
                    assertEquals(givenId, registerEntry.getRegisterId());
                    assertEquals(1, registerEntry.getQualificationsProofs().size());
                    assertEquals(DomainTestData.issuedBy, registerEntry.getQualificationsProofs().get(0).getIssuedBy());
                    assertEquals(1, registerEntry.getFishingLicenses().size());

                    final List<IdentificationDocument> newDocuments = registerEntry.getIdentificationDocuments();
                    assertEquals(4, newDocuments.size());

                    final List<IdentificationDocument> cards = newDocuments.stream()
                            .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                            .toList();
                    assertEquals(2, cards.size()); // old card plus new card

                    final List<IdentificationDocument> pdfs = newDocuments.stream()
                            .filter(document -> document.getType() == IdentificationDocumentType.PDF)
                            .toList();
                    assertEquals(2, pdfs.size()); // old pdf plus new pdf
                });
    }

    @Test
    @DisplayName("Test whether aggregate handlers OrderReplacementCardCommand change the issuing state of the fishing license.")
    @ExtendWith(AxonFixture.class)
    void testIssuedByFederalStateChanged(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();
        final UserDetails givenUserDetails = DomainTestData.createUserDetails(UserRole.OFFICIAL);

        final FishingLicense givenFishingLicense = new FishingLicense();

        givenFishingLicense.setNumber("LN12345");

        fixture
                .givenCommands(
                        new CreateFishingCertificateCommand(givenId,
                                DomainTestData.jan_2024,
                                givenPerson,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        ),
                        new MoveJurisdictionCommand(givenId, DomainTestData.createJurisdictionConsentInfo(), "anySalt", emptyList(), givenUserDetails),
                        new CreateRegularLicenseCommand(givenId, "anySalt", DomainTestData.createConsentInfo(), givenPerson, List.of(DomainTestData.createFee()), emptyList(), givenUserDetails))
                .when(new OrderReplacementCardCommand(
                        givenId,
                        givenFishingLicense.getNumber(),
                        givenPerson,
                        "anySalt",
                        List.of(DomainTestData.createFee()),
                        List.of(),
                        DomainTestData.createConsentInfo(),
                        givenUserDetails))
                .expectSuccessfulHandlerExecution()
                .expectState(registerEntry -> {
                    final FederalState actualState = registerEntry.getFishingLicenses().get(0).getIssuingFederalState();
                    assertEquals(givenUserDetails.getFederalState(), actualState.toString());
                });

    }
}
