package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class CreateVacationLicenseCommandProcessor implements CommandProcessor<CreateVacationLicenseCommand> {

    private final FishingLicenseFactory fishingLicenseFactory;
    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(CreateVacationLicenseCommand command, RegisterEntry registerEntry) {
        VacationLicenseCreatedEvent event = createEvent(command);
        return List.of(event);
    }

    private VacationLicenseCreatedEvent createEvent(CreateVacationLicenseCommand command) {
        final FishingLicense fishingLicense = fishingLicenseFactory.createVacationLicense(
                command.registerId(),
                command.validityPeriod(),
                FederalState.valueOf(command.userDetails().getFederalState())
        );

        final List<IdentificationDocument> identificationDocuments = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, command.registerId());
        command.taxes().forEach(t -> identificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));

        return new VacationLicenseCreatedEvent(
                command.registerId(),
                command.person(),
                command.salt(),
                command.consentInfo(),
                command.fees(),
                command.taxes(),
                identificationDocuments,
                fishingLicense,
                command.userDetails().getOffice(),
                null,
                SubmissionType.ANALOG
        );
    }
}
