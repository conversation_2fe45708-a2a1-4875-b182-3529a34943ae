package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.ConsentInfoValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FederalStateValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FeeValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.OSValidationHelper;
import de.adesso.fischereiregister.core.validation.helper.PersonValidatorHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSCreateVacationLicenseCommandValidator extends AbstractValidator implements CommandValidator<OSCreateVacationLicenseCommand> {

    public OSCreateVacationLicenseCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(OSCreateVacationLicenseCommand command) throws AggregateValidationException {
        // here we don't need to validate any address as for paying a tax there is no documents delivered
        PersonValidatorHelper.validate(command.person(), countryService, validationResult, true);

        FederalStateValidatorHelper.validateFederalState(command.federalState().toString(), validationResult);
        FeeValidatorHelper.validateFee(command.fee(), validationResult);
        ConsentInfoValidatorHelper.validate(command.consentInfo(), validationResult);
        validateValidityPeriod(command.validityPeriod());

        OSValidationHelper.validateOSRequiredFields(command.serviceAccountId(), command.osPaymentTransactionId(), command.inboxReference(), validationResult);


        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
