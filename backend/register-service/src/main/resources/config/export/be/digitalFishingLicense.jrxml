<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="digitalFishingLicense" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="licenseNumber" class="java.lang.String"/>
	<parameter name="documentId" class="java.lang.String"/>
	<parameter name="qrcode" class="java.lang.String"/>
	<parameter name="person" class="de.adesso.fischereiregister.core.model.Person"/>

	<background height="55" splitType="Stretch"/>
	<title splitType="Stretch"/>
	<pageHeader height="87">
		<element kind="image" uuid="1eda496c-4841-436b-9aba-b429d816884c" stretchType="ElementGroupHeight" x="378" y="0" width="128" height="77">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/be/flag.png")]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="image" uuid="2726210c-97bb-4e7b-a582-edab07d2a9b5" x="0" y="0" width="135" height="80">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/logoFisch.png")]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
	</pageHeader>
	<detail>
		<band height="490" splitType="Stretch">
			<element kind="textField" uuid="a98510f6-d7d4-4117-8950-be4e88e892ea" x="100" y="150" width="450" height="20">
				<expression><![CDATA[$P{person}.getFirstname()]]></expression>
			</element>
			<element kind="staticText" uuid="7828148a-db18-49cd-bae8-5462e8de9259" x="0" y="150" width="100" height="20" bold="true">
				<text><![CDATA[Vorname]]></text>
			</element>
			<element kind="textField" uuid="2a22dddf-80cf-4867-9895-a1eae2e7dbc0" x="100" y="170" width="450" height="20">
				<expression><![CDATA[$P{person}.getLastname()]]></expression>
			</element>
			<element kind="staticText" uuid="c9f11bb5-96b4-4e75-a4b6-48317d3ade39" x="0" y="170" width="100" height="20" bold="true">
				<text><![CDATA[Nachname]]></text>
			</element>
			<element kind="textField" uuid="21a989f2-53fd-46e3-a137-54699347558e" x="100" y="190" width="450" height="20">
				<expression><![CDATA[$P{person}.getBirthname()]]></expression>
			</element>
			<element kind="staticText" uuid="71e6fe81-f764-45c5-89bc-cd668b121c2f" x="0" y="190" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsname]]></text>
			</element>
			<element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="100" y="230" width="450" height="20">
				<expression><![CDATA[$P{person}.getBirthplace()]]></expression>
			</element>
			<element kind="staticText" uuid="61f07258-6dc7-4287-b698-a212b2303eb6" x="0" y="230" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsort]]></text>
			</element>
			<element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="100" y="210" width="450" height="20">
				<expression><![CDATA[($P{person}.getBirthdate())]]></expression>
			</element>
			<element kind="staticText" uuid="e5a6b7f5-d0e8-43be-8f9a-8972f1de673f" x="0" y="210" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsdatum]]></text>
			</element>
			<element kind="staticText" uuid="1af9a004-407f-407c-9831-5905e8874c5e" x="0" y="130" width="100" height="20" bold="true">
				<text><![CDATA[Title]]></text>
			</element>
			<element kind="textField" uuid="9214262d-750d-45df-9140-543d5c0d38a1" x="100" y="130" width="440" height="20" blankWhenNull="true">
				<expression><![CDATA[$P{person}.getTitle()]]></expression>
			</element>
			<element kind="staticText" uuid="5aea1e95-d859-4189-86ff-8b82c5a643a2" x="0" y="250" width="100" height="20" bold="true">
				<text><![CDATA[Fischereischein-ID]]></text>
			</element>
			<element kind="textField" uuid="8040435d-07d2-4952-afba-1d1612773b46" x="100" y="250" width="450" height="20">
				<expression><![CDATA[$P{licenseNumber}]]></expression>
			</element>
			<element kind="staticText" uuid="b50734ce-8dd1-4b54-bcb3-fffde50caeb0" x="0" y="270" width="100" height="20" bold="true">
				<text><![CDATA[Dokument-ID]]></text>
			</element>
			<element kind="textField" uuid="1720ffaf-b48f-447e-af2f-685e18eaf9a3" x="100" y="270" width="450" height="20">
				<expression><![CDATA[$P{documentId}]]></expression>
			</element>
			<element kind="staticText" uuid="b54e62de-e509-45af-938e-06b93b5cc625" x="-20" y="56" width="595" height="30" fontSize="20.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[Digitaler Fischereischein]]></text>
			</element>
			<element kind="component" uuid="7ac8cc27-80c9-452c-87ba-d8caa94ac7df" x="300" y="310" width="95" height="95">
				<component kind="barcode4j:QRCode" errorCorrectionLevel="H">
					<codeExpression><![CDATA[$P{qrcode}]]></codeExpression>
				</component>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
		</band>
	</detail>
	<pageFooter height="16"/>
</jasperReport>
